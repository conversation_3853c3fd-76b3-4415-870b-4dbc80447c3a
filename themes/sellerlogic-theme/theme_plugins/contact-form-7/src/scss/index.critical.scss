.wpcf7-form{
	.wpcf7-not-valid-tip{
		font-size: 12px;
		line-height: 2;
		color: #E94F37;
	}
	&.submitting .wpcf7-submit{
		pointer-events: none;
		opacity: .64;
	}
}

.contact-flex{

	.contact-row.submit-wrap .contact-input{
		margin-bottom: 0;
	}

	.contact-input{
		margin-bottom: 16px;
		.wpcf7-spinner{
			position: absolute;
		}
	}

	input:not([type=submit]), select, textarea{
		margin-bottom: 0;

		&.wpcf7-not-valid{
			border-color: #E94F37;
		}
	}

	.submit-wrap{
		display: flex;
		justify-content: center;
		flex-wrap: wrap;

		.policy-text{
			text-align: right;
			flex-basis: 50%;
			flex-grow: 1;
			margin-top: 16px;

			a{
				white-space: nowrap;
			}

			@include tablet(){
				text-align: inherit;
				flex-basis: 100%;
				margin-top: 16px;
			}
		}
	}
}


html[dir="rtl"] .contact-form{
	p.theme-wp-block, p:empty{
		display: none;
	}
}