$margin-between: 15px;
$margin-between-tablet: 10px;

.theme-block-text-and-text{
	position: relative;

	> .tat-column-wrap-wrapper{
	}

	&.is-style-center-separate-line{
		&:before{
			content: '';
			position: absolute;
			top: 0;
			bottom: 0;
			left: 50%;
			width: 1px;
			background-color: #dee1ea;
		}
	}

	.inner-wrap-column-content{
		max-width: 100%;

		.inner-column-content{
			clear: both;
			width: 100%;
		}
	}
}




@mixin default_media_centering(){
	max-width: 484px;
	margin-right: auto;
	margin-left: auto;
	text-align: center;
}

@mixin default_media_centering_for_main_header(){
	max-width: 568px;
	text-align: center;
}

@mixin theme_default_media_centering(){
	.wp-block-image, .wp-block-embed, .theme-block-image-zoom{
		&:not([class*="xl-align-"]):not([class*="lg-align-"]):not([class*="md-align-"]):not([class*="sm-align-"]){
			@include default_media_centering();

			.theme-block-main-header &{
				@include default_media_centering_for_main_header();
			}
		}

		@include align-breakpoints() using($class, $size){
			@if $class == align-none{
				@include default_media_centering();

				.theme-block-main-header &{
					@include default_media_centering_for_main_header();
				}
			}
		}
	}
}

@mixin editor_default_media_centering(){
	div:not([data-align]) >{
		.wp-block-image, .wp-block-embed, [data-type="theme/image-zoom"]{
			&:not([data-align]){
				@include default_media_centering();

				.theme-block-main-header &{
					@include default_media_centering_for_main_header();
				}
			}
		}
	}
}

@mixin column-size-40-50(){
	&:first-child{
		width: 50%;

		.inner-column-content{
			@include mq($from: 1280){
				max-width: 515px;
			}

			@include mq($until: 1280){
				@include mq($from: 1024){
					@include calc-fluid(484, 515, 1024, 1280, 'max-width', 'px');
				}
			}
		}

		+ div{
		}
	}
}

@mixin column-size-50-40(){
	&{
		width: 50%;

		+ div{
			.inner-column-content{
				@include mq($from: 1280){
					max-width: 515px;
				}

				@include mq($until: 1280){
					@include mq($from: 1024){
						@include calc-fluid(484, 515, 1024, 1280, 'max-width', 'px');
					}
				}
			}
		}
	}
}

@mixin column-size-30-60(){
	&{
		width: 50%;
	}

	@include from-tablet(){
		&:first-child{
			width: calc(#{100 - 57.265625%} - #{$margin-between});

			.inner-column-content{
				max-width: 407px;
			}

			+ div{
				width: calc(57.265625% + #{$margin-between});
			}
		}
	}
}

@mixin column-size-60-30(){
	&{
		width: 50%;
	}

	@include from-tablet(){
		&:first-child{
			width: calc(57.265625% - #{$margin-between});

			+ div{
				width: calc(#{100 - 57.265625%} + #{$margin-between});

				.inner-column-content{
					max-width: 407px;
				}
			}
		}
	}
}

@mixin column-size-40-60(){
	&{
		width: calc(#{100 - 57.265625%} - #{$margin-between});

		+ div{
			width: calc(#{57.265625%} + #{$margin-between});
		}

		@include tablet(){
			width: calc(#{100 - 57.265625%} - #{$margin-between-tablet});

			+ div{
				width: calc(#{57.265625%} + #{$margin-between-tablet});
			}
		}
	}
}

@mixin column-size-60-40(){
	&{
		width: calc(#{57.265625%} + #{$margin-between});

		+ div{
			width: calc(#{100 - 57.265625%} - #{$margin-between});
		}

		@include tablet(){
			width: calc(#{57.265625%} + #{$margin-between-tablet});

			+ div{
				width: calc(#{100 - 57.265625%} - #{$margin-between-tablet});
			}
		}
	}
}

@mixin column-size-30-70(){
	&{
		flex-grow: 1;
		width: auto;

		@include mq($from: 1280){
			width: calc(#{100 - 57.265625%} - #{$margin-between});
		}
		@include mq($until: 1024){
			width: calc(31.8217357% - #{$margin-between-tablet});
		}

		+ div{
			width: 100%;
			@include calc-fluid(625 + $margin-between, 733 + $margin-between, 1024, 1280, 'max-width', 'px');

			@include mq($from: 1280){
				max-width: none;
				width: calc(57.265625% + #{$margin-between});
			}
			@include mq($until: 1024){
				max-width: 625px;
				width: calc(#{100 - 31.8217357%} + #{$margin-between-tablet});
			}
			@include mq($until: 900){
				max-width: none;
			}
		}
	}
}

@mixin column-size-70-30(){
	&{
		width: 100%;
		@include calc-fluid(625 + $margin-between, 733 + $margin-between, 1024, 1280, 'max-width', 'px');

		@include mq($from: 1280){
			max-width: none;
			width: calc(57.265625% + #{$margin-between});
		}
		@include mq($until: 1024){
			max-width: 625px;
			width: calc(#{100 - 31.8217357%} + #{$margin-between-tablet});
		}
		@include mq($until: 900){
			max-width: none;
		}

		+ div{
			flex-grow: 1;
			width: auto;

			@include mq($from: 1280){
				width: calc(#{100 - 57.265625%} - #{$margin-between});
			}
			@include mq($until: 1024){
				width: calc(31.8217357% - #{$margin-between-tablet});
			}
		}
	}
}