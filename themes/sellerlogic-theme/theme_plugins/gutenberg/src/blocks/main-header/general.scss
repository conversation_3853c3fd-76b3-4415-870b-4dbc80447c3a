header:not(.has-second-nav){
	+ .wrap-content{
		.gutenberg-content-wrap{
			> .theme-block-main-header:first-child{
				&.is-style-full-height, &:not(.is-style-default):not(.is-style-full-height-and-logo-carousel):not(.is-style-full-height-and-new-logo-carousel){
					min-height: calc(100vh - #{$main-nav-height});

					@include mq($until: 1350){
						@include from-tablet(){
							min-height: calc(100vh - #{$main-nav-height} * 2);
						}
					}

					@include tablet(){
						min-height: calc(100vh - #{$main-nav-mobile-height});
					}
				}

				&.is-style-full-height-and-logo-carousel{
					min-height: calc(100vh - #{$main-nav-height} - #{$logo-carousel-height});

					@include mq($until: 1350){
						@include from-tablet(){
							min-height: calc(100vh - #{$main-nav-height} * 2 - #{$logo-carousel-height});
						}
					}

					@include mq($until: 1000){
						min-height: calc(100vh - #{$main-nav-height} - #{$logo-carousel-mobile-height});

						@include mq($until: 1350){
							@include from-tablet(){
								min-height: calc(100vh - #{$main-nav-height} * 2 - #{$logo-carousel-mobile-height});
							}
						}
					}

					@include tablet(){
						min-height: calc(100vh - #{$main-nav-mobile-height} - #{$logo-carousel-mobile-height});
					}
				}

				&.is-style-full-height-and-new-logo-carousel{
					min-height: calc(100vh - #{$main-nav-height} - #{$logo-carousel-new-height});

					@include mq($until: 1350){
						@include from-tablet(){
							min-height: calc(100vh - #{$main-nav-height} * 2 - #{$logo-carousel-new-height});
						}
					}

					@include mq($until: 1000){
						min-height: calc(100vh - #{$main-nav-height} - #{$logo-carousel-new-mobile-height});

						@include mq($until: 1350){
							@include from-tablet(){
								min-height: calc(100vh - #{$main-nav-height} * 2 - #{$logo-carousel-new-mobile-height});
							}
						}
					}

					@include tablet(){
						min-height: calc(100vh - #{$main-nav-mobile-height} - #{$logo-carousel-new-mobile-height});
					}
				}
			}
		}
	}
}

header.has-gutenberg-nav{
	+ .wrap-content{
		.gutenberg-content-wrap{
			> .theme-block-second-nav:first-child{
				+ .theme-second-nav-placeholder{
					+ .theme-block-main-header{
						&.is-style-full-height, &:not(.is-style-default):not(.is-style-full-height-and-logo-carousel):not(.is-style-full-height-and-new-logo-carousel){
							min-height: calc(100vh - #{$main-nav-height} - #{$second-nav-gutenberg-height});

							@include tablet(){
								min-height: calc(100vh - #{$main-nav-mobile-height} - #{$second-nav-gutenberg-mobile-height});
							}
						}

						&.is-style-full-height-and-logo-carousel{
							min-height: calc(100vh - #{$main-nav-height} - #{$second-nav-gutenberg-height} - #{$logo-carousel-height});
							@include mq($until: 1000){
								min-height: calc(100vh - #{$main-nav-height} - #{$second-nav-gutenberg-height} - #{$logo-carousel-mobile-height});
							}

							@include tablet(){
								min-height: calc(100vh - #{$main-nav-mobile-height} - #{$second-nav-gutenberg-mobile-height} - #{$logo-carousel-mobile-height});
							}
						}

						&.is-style-full-height-and-new-logo-carousel{
							min-height: calc(100vh - #{$main-nav-height} - #{$second-nav-gutenberg-height} - #{$logo-carousel-new-height});
							@include mq($until: 1000){
								min-height: calc(100vh - #{$main-nav-height} - #{$second-nav-gutenberg-height} - #{$logo-carousel-new-mobile-height});
							}

							@include tablet(){
								min-height: calc(100vh - #{$main-nav-mobile-height} - #{$second-nav-gutenberg-mobile-height} - #{$logo-carousel-new-mobile-height});
							}
						}
					}
				}
			}
		}
	}
}

header.has-blog-nav{
	+ .wrap-content{
		.gutenberg-content-wrap{
			> .theme-block-main-header:first-child{
				&.is-style-full-height, &:not(.is-style-default):not(.is-style-full-height-and-logo-carousel):not(.is-style-full-height-and-new-logo-carousel){
					min-height: calc(100vh - #{$main-nav-height} - #{$second-blog-nav-height});

					@include tablet(){
						min-height: calc(100vh - #{$main-nav-mobile-height} - #{$second-blog-nav-mobile-height});
					}
				}

				&.is-style-full-height-and-logo-carousel{
					min-height: calc(100vh - #{$main-nav-height} - #{$second-blog-nav-height} - #{$logo-carousel-height});
					@include mq($until: 1000){
						min-height: calc(100vh - #{$main-nav-height} - #{$second-blog-nav-height} - #{$logo-carousel-mobile-height});
					}

					@include tablet(){
						min-height: calc(100vh - #{$main-nav-mobile-height} - #{$second-blog-nav-mobile-height} - #{$logo-carousel-mobile-height});
					}
				}

				&.is-style-full-height-and-new-logo-carousel{
					min-height: calc(100vh - #{$main-nav-height} - #{$second-blog-nav-height} - #{$logo-carousel-new-height});
					@include mq($until: 1000){
						min-height: calc(100vh - #{$main-nav-height} - #{$second-blog-nav-height} - #{$logo-carousel-new-mobile-height});
					}

					@include tablet(){
						min-height: calc(100vh - #{$main-nav-mobile-height} - #{$second-blog-nav-mobile-height} - #{$logo-carousel-new-mobile-height});
					}
				}
			}
		}
	}
}




.theme-block-main-header{
	display: flex;
	background-size: cover;

	/*&.background-is-image{
		img{
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}*/
}
.theme-block-main-header.wp-block-cover, .theme-block-main-header{
	padding: 24px;
	@include tablet(){
		padding: 12px;
	}
	&.is-style-default{

	}

	&.is-style-full-height, &:not(.is-style-default):not(.is-style-full-height-and-logo-carousel){
		min-height: 100vh;
	}

	&.is-style-full-height-and-logo-carousel{
		min-height: 100vh;
		min-height: calc(100vh - #{$logo-carousel-height});
		@include mq($until: 1000){
			min-height: calc(100vh - #{$logo-carousel-mobile-height});
		}
	}

	&.is-style-full-height-and-new-logo-carousel{
		min-height: 100vh;
		min-height: calc(100vh - #{$logo-carousel-new-height});
		@include mq($until: 1000){
			min-height: calc(100vh - #{$logo-carousel-new-mobile-height});
		}
	}
}

html[dir="rtl"]{
	// .gutenberg-content-wrap.gutenberg-content-wrap .bg-cover{
	// 	background-position: 10% center !important;
	// }
	.tat-column-wrapper{
		.theme-block-text-and-text-column{
			width: 50%!important;
			flex-basis: 50%!important;
		}
	}
}

html[dir="rtl"]{
	.theme-block-main-header .theme-block-text-and-text{
		.tat-column-wrapper{
			flex-direction: row-reverse;
			justify-content: flex-end;
		}
	}
}